import { Box, Button, Icon, IconButton, Text } from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useCallback, useEffect, useReducer } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { HiArrowLeft } from 'react-icons/hi';
import { Link } from 'react-router-dom';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import { TextInput } from 'shared/components';
import { LoginRoute } from 'shared/constants/routes';
import { useHandleGenericError } from 'shared/hooks/alerts';
import { LoginMethods } from 'shared/types';
import { z } from 'zod';

import { useSendPasswordResetEmailMutation } from '../api';
import { ResetPasswordFirstStepMsg } from './ResetPasswordFirstStepMsg';

const ResetPasswordSchema = z.object({
  email: z.string().email('invalid-format'),
});

type ResetPasswordSchemaType = z.infer<typeof ResetPasswordSchema>;

enum ReducerActionsTypes {
  EmailIsSent = 'EMAIL_IS_SENT',
  EmailAddressIsIncorrect = 'EMAIL_ADRESS_IS_INCORRECT',
  LinkIsResent = 'LINK_IS_RESENT',
}

type ReducerActions =
  | typeof ReducerActionsTypes.EmailIsSent
  | typeof ReducerActionsTypes.EmailAddressIsIncorrect
  | typeof ReducerActionsTypes.LinkIsResent;

type ReducerState = {
  isEmailSent: boolean;
  email: string;
  isLinkResent: boolean;
};

const reducer = (
  state: ReducerState,
  action: { type: ReducerActions; data?: string },
): ReducerState => {
  switch (action.type) {
    case ReducerActionsTypes.EmailIsSent:
      return {
        ...state,
        isEmailSent: true,
        email: action?.data || '',
        isLinkResent: false,
      };
    case ReducerActionsTypes.EmailAddressIsIncorrect:
      return { ...state, isEmailSent: false };
    case ReducerActionsTypes.LinkIsResent:
      return { ...state, isLinkResent: true };
    default:
      return state;
  }
};

export const ResetPasswordFirstStep = () => {
  const { t } = useTranslation(['reset-password', 'common']);
  const handleGenericError = useHandleGenericError();

  const [state, dispatch] = useReducer(reducer, {
    isEmailSent: false,
    email: '',
    isLinkResent: false,
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm<ResetPasswordSchemaType>({
    defaultValues: { email: state.email },
    resolver: zodResolver(ResetPasswordSchema),
  });

  const [sendPasswordResetEmail, { loading }] =
    useSendPasswordResetEmailMutation({
      onError: (error) => {
        handleGenericError(error);
      },
    });

  const onSubmit = useCallback(
    async ({ email }: ResetPasswordSchemaType) => {
      const result = await sendPasswordResetEmail({
        variables: { email },
      });

      if (result !== null) {
        dispatch({ type: ReducerActionsTypes.EmailIsSent, data: email });
      }
    },
    [sendPasswordResetEmail],
  );

  const resendLink = useCallback(async () => {
    const result = await sendPasswordResetEmail({
      variables: { email: state.email },
    });

    if (result !== null) {
      dispatch({ type: ReducerActionsTypes.LinkIsResent });
    }
  }, [sendPasswordResetEmail, state.email]);

  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    state.email && !state.isEmailSent && setValue('email', state.email);
  }, [state, setValue]);

  const goToChangeEmail = useCallback(() => {
    dispatch({ type: ReducerActionsTypes.EmailAddressIsIncorrect });
  }, []);

  return (
    <Box pb={12} pt={[6, '150px']}>
      {state.isEmailSent ? (
        <ResetPasswordFirstStepMsg
          email={state.email}
          goToChangeEmail={goToChangeEmail}
          isLinkResent={state.isLinkResent}
          resendLink={resendLink}
        />
      ) : (
        <>
          <Box position="relative">
            <Link to={LoginRoute.format({ method: LoginMethods.Password })}>
              <IconButton
                aria-label="back"
                bottom={0}
                data-cy="back-to-login-icon-btn"
                display={['none', null, 'inline-flex']}
                left={-16}
                margin="auto"
                position="absolute"
                top={0}
                variant="ghost"
              >
                <Icon as={HiArrowLeft} boxSize={6} color="primary.800" />
              </IconButton>
            </Link>
            <Text mb={[5, 8]} textStyle="h2">
              {t('reset-password:first-step.title')}
            </Text>
          </Box>

          <Text mb={[4, 5]} textStyle="body1">
            {t('reset-password:first-step.description')}
          </Text>
          <Box as="form" onSubmit={handleSubmit(onSubmit)}>
            <TextInput
              data-cy="send-to-email-input"
              inputMode="email"
              label={t('reset-password:first-step.label')}
              mb={10}
              {...register('email')}
              autoFocus
              error={
                errors.email?.message
                  ? t(`common:forms.${errors.email.message}`)
                  : undefined
              }
              disabled={loading}
            />
            <Button
              colorScheme={ColorSchemes.PRIMARY}
              data-cy="send-link-to-email-btn"
              loading={loading}
              type="submit"
              width="full"
            >
              {t('reset-password:first-step.submit')}
            </Button>
          </Box>
        </>
      )}
    </Box>
  );
};
