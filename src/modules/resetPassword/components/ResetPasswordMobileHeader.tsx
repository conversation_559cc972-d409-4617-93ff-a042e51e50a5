import { Box, Icon, IconButton } from '@chakra-ui/react';
import { HiArrowLeft } from 'react-icons/hi';
import { Link } from 'react-router-dom';
import { EstoLogo } from 'shared/components';
import { LoginRoute } from 'shared/constants/routes';
import { LoginMethods } from 'shared/types';

export const FirstStepMobileHeader = () => {
  return (
    <Box
      alignItems="center"
      borderBottom="1px solid"
      borderColor="neutral.150"
      boxSizing="content-box"
      display="flex"
      height={14}
      justifyContent="center"
      pl={5}
      position="relative"
      pr="0.875rem"
    >
      <IconButton
        aria-label="back"
        as={Link}
        bottom={0}
        icon={<Icon as={HiArrowLeft} boxSize={6} color="primary.800" />}
        left={1}
        margin="auto"
        position="absolute"
        to={LoginRoute.format({ method: LoginMethods.Password })}
        top={0}
        variant="ghost"
      />
      <EstoLogo height={5} width="auto" />
    </Box>
  );
};
