import {
  <PERSON>dal,
  ModalBody,
  ModalContent,
  <PERSON>dal<PERSON><PERSON>er,
  ModalOverlay,
} from '@chakra-ui/modal';
import {
  Box,
  type BoxProps,
  Icon,
  IconButton,
  Text,
  useBreakpointValue,
} from '@chakra-ui/react';
import type React from 'react';
import { Suspense, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Loader, ModalCloseButton, StarIcon } from 'shared/components';
import { CypressTerminalKeys } from 'shared/constants/cypress-keys/terminal-page';
import { useCashierApplicationSalesBonus } from 'shared/hooks/application';
import { useFormattedAmount } from 'shared/hooks/utils';

export const CashierBonus = () => {
  const shouldHaveSmallerIcons = useBreakpointValue({ base: true, sm: false });
  const { t } = useTranslation('terminal');
  const [isModalOpen, setIsModalOpen] = useState(false);

  const onClose = () => {
    setIsModalOpen(false);
  };

  const onOpen = () => {
    setIsModalOpen(true);
  };

  return (
    <>
      <IconButton
        minW={shouldHaveSmallerIcons ? 'unset' : 'auto'}
        aria-label="calculator"
        boxSize={shouldHaveSmallerIcons ? 10 : 14}
        icon={
          <Icon
            as={StarIcon}
            boxSize={shouldHaveSmallerIcons ? 4 : 6}
            color="green.600"
          />
        }
        onClick={onOpen}
        variant="ghost"
      />
      <Modal isOpen={isModalOpen} onClose={onClose} scrollBehavior="inside">
        <ModalOverlay display={['none', 'flex']} />
        <ModalContent data-cy={CypressTerminalKeys.CALCULATOR_FORM}>
          <ModalHeader>{t('cashier-bonus.title')}</ModalHeader>
          <ModalCloseButton iconColor="neutral.must" />
          <ModalBody>
            <Text textStyle="body2" marginBottom={6}>
              {t('cashier-bonus.description')}
            </Text>
            <Suspense fallback={<Loader />}>
              <CashierBonusModalBody />
            </Suspense>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};

export const CashierBonusModalBody: React.FC<BoxProps> = (chakraProps) => {
  const { t } = useTranslation('terminal');
  const { data: cashierBonusData } = useCashierApplicationSalesBonus();

  return (
    <Box
      display="flex"
      flexDirection="column"
      justifyContent="space-between"
      gap={2}
      w="100%"
      {...chakraProps}
    >
      <CashierBonusItem
        amount={cashierBonusData?.cashier_bonus?.all_time_bonus_amount || 0}
        label={t('cashier-bonus.total')}
      />
      <CashierBonusItem
        amount={cashierBonusData?.cashier_bonus?.all_time_bonus_paid || 0}
        label={t('cashier-bonus.paid')}
      />
      <CashierBonusItem
        amount={cashierBonusData?.cashier_bonus?.all_time_bonus_unpaid || 0}
        label={t('cashier-bonus.awaiting')}
      />
    </Box>
  );
};

type CashierBonusItemProps = {
  label: string;
  amount: number;
};

const CashierBonusItem: React.FC<CashierBonusItemProps> = ({
  label,
  amount,
}) => {
  const formattedAmount = useFormattedAmount(amount, {
    currency: ' €',
  });

  return (
    <Box
      display="flex"
      justifyContent="space-between"
      border="1px solid"
      borderColor="neutral.100"
      borderRadius="md"
      p={4}
    >
      <Text color="neutral.800" textStyle="body">
        {label}
      </Text>
      <Text color="green.600" textStyle="h4">
        {formattedAmount}
      </Text>
    </Box>
  );
};
