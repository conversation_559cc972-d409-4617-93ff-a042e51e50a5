import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  ModalOverlay,
} from '@chakra-ui/modal';
import {
  Box,
  Button,
  HStack,
  Icon,
  IconButton,
  Text,
  useBreakpointValue,
  useDisclosure,
  VStack,
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  PaymentInfo,
  PaymentPlanCard,
} from 'modules/terminal/components/payment-plan';
import {
  useCreditSettingsPeriodInfoByScheduleType,
  useTerminalPaymentPlanLimitsByScheduleType,
  useTerminalPaymentPlans,
} from 'modules/terminal/hooks';
import { useIsValidLoanAmountByScheduleType } from 'modules/terminal/Terminal.hooks';
import type { TerminalFormSchemaType } from 'modules/terminal/Terminal.schema';
import type { TerminalPaymentPlan } from 'modules/terminal/Terminal.types';
import { generateAmountHint } from 'modules/terminal/Terminal.utils';
import { useCallback, useMemo } from 'react';
import { Controller, useForm, useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { FiInfo } from 'react-icons/fi';
import type { ApplicationScheduleType } from 'shared/api';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import {
  ButtonWithLoader,
  CalculatorIcon,
  ModalCloseButton,
  NumericNumberInput,
} from 'shared/components';
import { CollapsibleText } from 'shared/components/CollapsibleText';
import { CypressTerminalKeys } from 'shared/constants/cypress-keys/terminal-page';
import { LocizeNamespaces } from 'shared/constants/localization-keys';
import { useShowError } from 'shared/hooks/alerts';

import {
  TerminalPaymentCalculatorSchema,
  type TerminalPaymentCalculatorSchemaType,
} from './TerminalPaymentCalculator.schema';

export const TerminalPaymentCalculator = () => {
  const { setValue: terminalFormSetValue, watch: terminalFormWatch } =
    useFormContext<TerminalFormSchemaType>();
  const [terminalFormAmount, terminalFormScheduleType, isPracticeMode] =
    terminalFormWatch(['amount', 'scheduleType', 'isPracticeMode']);

  const { t } = useTranslation([LocizeNamespaces.TERMINAL]);
  const getPaymentPlanAmountLimits =
    useTerminalPaymentPlanLimitsByScheduleType();
  const {
    control,
    formState: { errors, isValid, isSubmitting },
    watch,
    handleSubmit,
    reset,
    setValue,
  } = useForm<TerminalPaymentCalculatorSchemaType>({
    mode: 'onChange',
    resolver: zodResolver(
      TerminalPaymentCalculatorSchema({
        getPaymentPlanAmountLimits,
      }),
    ),
    defaultValues: {
      amount: 0,
      scheduleType: null,
    },
  });
  const [amount, selectedScheduleType] = watch(['amount', 'scheduleType']);

  const isValidAmount = useIsValidLoanAmountByScheduleType({
    amount,
    scheduleType: selectedScheduleType,
  });

  const { creditSettingsPeriodInfo } =
    useCreditSettingsPeriodInfoByScheduleType({
      scheduleType: selectedScheduleType,
      skip: !selectedScheduleType || !isValidAmount,
      netTotal: amount,
    });

  const paymentPlans = useTerminalPaymentPlans();

  const shouldShowPlanInfo = !!creditSettingsPeriodInfo && amount > 0;

  const selectedPaymentPlanSettings =
    useMemo((): Nullable<TerminalPaymentPlan> => {
      if (!selectedScheduleType) {
        return null;
      }

      return (
        paymentPlans.find(
          (plan) => plan.scheduleType === selectedScheduleType,
        ) ?? null
      );
    }, [paymentPlans, selectedScheduleType]);

  const amountHint = useMemo(() => {
    if (!selectedPaymentPlanSettings) {
      return;
    }

    return generateAmountHint({
      min: selectedPaymentPlanSettings.minLoanAmount,
      max: selectedPaymentPlanSettings.maxLoanAmount,
    });
  }, [selectedPaymentPlanSettings]);

  const { open, onOpen, onClose } = useDisclosure({
    onOpen: () => {
      reset({
        amount: terminalFormAmount,
        scheduleType: terminalFormScheduleType,
      });
    },
  });

  const showError = useShowError();
  const isInlinePlanInfo = useBreakpointValue([true, null, false]);
  const isHidePlansForInvalidAmount = useBreakpointValue([true, false]);
  const shouldHaveSmallerIcons = useBreakpointValue({ base: true, sm: false });

  const handlePaymentPlanClick = useCallback(
    (scheduleType: ApplicationScheduleType) => () => {
      setValue('scheduleType', scheduleType, {
        shouldValidate: true,
      });
    },
    [setValue],
  );

  const onSubmit = useCallback(
    ({ amount, scheduleType }: TerminalPaymentCalculatorSchemaType) => {
      try {
        terminalFormSetValue('amount', amount, {
          shouldDirty: true,
          shouldValidate: true,
        });
        terminalFormSetValue('scheduleType', scheduleType, {
          shouldDirty: true,
          shouldValidate: true,
        });
        onClose();
      } catch {
        showError(t('calculator.amount-error'));
      }
    },
    [onClose, showError, t, terminalFormSetValue],
  );

  return (
    <>
      <IconButton
        minW={shouldHaveSmallerIcons ? 'unset' : 'auto'}
        aria-label="calculator"
        boxSize={shouldHaveSmallerIcons ? 10 : 14}
        data-cy={CypressTerminalKeys.CALCULATOR_BUTTON}
        icon={
          <Icon
            as={CalculatorIcon}
            boxSize={shouldHaveSmallerIcons ? 4 : 6}
            color="primary.800"
          />
        }
        onClick={onOpen}
        variant="ghost"
      />
      <Modal isOpen={open} onClose={onClose} scrollBehavior="inside">
        <ModalOverlay display={['none', 'flex']} />
        <ModalContent data-cy={CypressTerminalKeys.CALCULATOR_FORM}>
          <ModalHeader>{t('calculator.title')}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {isInlinePlanInfo ? (
              <CollapsibleText mb={5}>
                <span>{t('calculator.disclaimer') || ''}</span>
              </CollapsibleText>
            ) : (
              <Text mb={6} textStyle="body2" whiteSpace="pre-wrap">
                {t('calculator.disclaimer')}
              </Text>
            )}
            <Controller
              control={control}
              name="amount"
              render={({ field: { onChange, ...rest } }) => (
                <NumericNumberInput
                  data-cy={CypressTerminalKeys.CALCULATOR_FORM_AMOUNT_INPUT}
                  disabled={!selectedScheduleType}
                  {...rest}
                  allowNegative={false}
                  autoFocus
                  defaultValue={amount}
                  error={
                    errors?.amount?.message
                      ? errors?.amount.message === 'required'
                        ? t('common:forms.required')
                        : amountHint
                      : undefined
                  }
                  hint={amountHint}
                  label={t('calculator.amount-label')}
                  onValueChange={({ floatValue }) => {
                    onChange(floatValue);
                  }}
                  suffix=" €"
                  valueIsNumericString
                />
              )}
            />

            {isHidePlansForInvalidAmount && !isValid ? (
              <Text
                bg="neutral.50"
                borderRadius="4px"
                mt={[12, 14]}
                px={3}
                py={2}
                textAlign="center"
                textStyle="body2"
              >
                {t('calculator.payment-info-empty')}
              </Text>
            ) : (
              <HStack alignItems="flex-start" mb={8} gap={4} w="100%">
                <VStack
                  alignItems="stretch"
                  minW={isInlinePlanInfo ? 'auto' : '412px'}
                  shouldWrapChildren
                  gap={2}
                  w="100%"
                >
                  {paymentPlans.map((plan, index) => {
                    const isSelected =
                      plan.scheduleType === selectedScheduleType;

                    return (
                      <PaymentPlanCard
                        bottomContent={
                          isInlinePlanInfo && isValid ? (
                            <PaymentInfo
                              creditSettingsPeriodInfo={
                                isSelected && creditSettingsPeriodInfo
                                  ? creditSettingsPeriodInfo
                                  : null
                              }
                              isDesktop
                              isPracticeMode={isPracticeMode}
                              scheduleType={plan.scheduleType}
                            />
                          ) : undefined
                        }
                        dataCy={`${CypressTerminalKeys.CALCULATOR_FORM_PAYMENT_PLAN_OPTION}-${index}`}
                        isPracticeMode={isPracticeMode}
                        isSelected={isSelected}
                        applicationAmount={amount}
                        key={plan.scheduleType}
                        onClick={handlePaymentPlanClick(plan.scheduleType)}
                        plan={plan}
                      />
                    );
                  })}
                </VStack>
                {!isInlinePlanInfo && (
                  <Box
                    alignSelf="stretch"
                    bg={shouldShowPlanInfo ? 'primary.100' : 'neutral.50'}
                    borderRadius="4px"
                    flexGrow={1}
                    maxW="144px"
                    minH="13rem"
                    minW="144px"
                    px={5}
                    py={3}
                  >
                    {!shouldShowPlanInfo ? (
                      <Box
                        alignItems="center"
                        data-cy={
                          CypressTerminalKeys.CALCULATOR_FORM_PAYMENT_PLAN_EMPTY_INFO
                        }
                        display="flex"
                        flexDirection="column"
                        h="100%"
                        justifyContent="center"
                        w="100%"
                      >
                        <Icon as={FiInfo} boxSize={6} mb={2} />
                        <Text textAlign="center" textStyle="body2">
                          {t('calculator.payment-info-empty')}
                        </Text>
                      </Box>
                    ) : (
                      <Box
                        alignItems="flex-end"
                        data-cy={
                          CypressTerminalKeys.CALCULATOR_FORM_PAYMENT_PLAN_INFO
                        }
                        display="flex"
                        flexDirection="column"
                        h="100%"
                        w="100%"
                      >
                        {!!creditSettingsPeriodInfo &&
                          !!selectedScheduleType && (
                            <PaymentInfo
                              creditSettingsPeriodInfo={
                                creditSettingsPeriodInfo || null
                              }
                              isDesktop
                              isPracticeMode={isPracticeMode}
                              scheduleType={selectedScheduleType}
                            />
                          )}
                      </Box>
                    )}
                  </Box>
                )}
              </HStack>
            )}
          </ModalBody>
          <ModalFooter
            borderColor="neutral.150"
            borderTop={['none', '1px solid']}
          >
            <HStack gap={3}>
              <Button
                colorScheme={ColorSchemes.SECONDARY}
                data-cy={CypressTerminalKeys.CALCULATOR_FORM_CANCEL_BUTTON}
                display={['none', 'block']}
                onClick={onClose}
                size="sm"
              >
                {t('common:modal.close')}
              </Button>
              <ButtonWithLoader
                dataCy={CypressTerminalKeys.CALCULATOR_FORM_SUBMIT_BUTTON}
                display={
                  isHidePlansForInvalidAmount && !isValid ? 'none' : 'block'
                }
                disabled={!isValid}
                loading={isSubmitting}
                onClick={handleSubmit(onSubmit)}
                size="sm"
                w={['100%', 'auto']}
              >
                {t('calculator.use-button')}
              </ButtonWithLoader>
            </HStack>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
};
