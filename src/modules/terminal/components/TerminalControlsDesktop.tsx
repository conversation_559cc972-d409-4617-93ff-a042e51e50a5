import { Box, HStack, Icon, IconButton, Switch, Text } from '@chakra-ui/react';
import { Tooltip } from '@chakra-ui/tooltip';
import {
  useHasCashierAnyBonus,
  useIsCashierBonusEnabled,
} from 'modules/applications/list/controls/hooks';
import type { TerminalFormSchemaType } from 'modules/terminal/Terminal.schema';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { FiRotateCw } from 'react-icons/fi';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import { ConfirmationPopover } from 'shared/components';
import { CypressTerminalKeys } from 'shared/constants/cypress-keys/terminal-page';

import { CashierBonus } from './cashier-bonus/CashierBonus';
import { TerminalPaymentCalculator } from './payment-calculator';

export const TerminalControlsDesktop = () => {
  const { t } = useTranslation('terminal');
  const {
    watch,
    setValue,
    reset: handleFormReset,
  } = useFormContext<TerminalFormSchemaType>();
  const isPracticeMode = watch('isPracticeMode');
  const isCashierBonusEnabled = useIsCashierBonusEnabled();
  const hasCashierAnyBonus = useHasCashierAnyBonus();

  const handlePracticeModeToggle = () => {
    setValue('isPracticeMode', !isPracticeMode, { shouldDirty: true });
  };

  return (
    <Box alignItems="center" display="flex" h={16} px={2}>
      <HStack alignItems="center" ml={4} gap={2}>
        <Switch
          colorScheme={ColorSchemes.PRIMARY}
          data-cy={CypressTerminalKeys.PRACTICE_MODE_TOGGLER}
          isChecked={isPracticeMode}
          onChange={handlePracticeModeToggle}
        />
        <Text
          color={isPracticeMode ? 'neutral.900' : 'neutral.800'}
          textStyle={isPracticeMode ? 'body2-highlight' : 'body2'}
        >
          {t(`practice-mode.${isPracticeMode ? 'on' : 'off'}`)}
        </Text>
      </HStack>

      <Box alignItems="center" display="flex" ml="auto">
        {isCashierBonusEnabled && hasCashierAnyBonus && <CashierBonus />}
        <TerminalPaymentCalculator />
        <ConfirmationPopover
          actionText={t('clear-terminal.action')}
          cyPrefix={CypressTerminalKeys.RESET_BUTTON_CONFIRM}
          onAction={handleFormReset}
          popoverPlacement="bottom-end"
          title={t('clear-terminal.title')}
          trigger={
            <Box>
              <Tooltip
                arrowSize={8}
                hasArrow
                label={t('clear-terminal.tooltip')}
                placement="bottom-end"
              >
                <IconButton
                  aria-label="reset"
                  data-cy={CypressTerminalKeys.RESET_BUTTON}
                  variant="ghost"
                >
                  <Icon as={FiRotateCw} boxSize={6} color="primary.800" />
                </IconButton>
              </Tooltip>
            </Box>
          }
        />
      </Box>
    </Box>
  );
};
