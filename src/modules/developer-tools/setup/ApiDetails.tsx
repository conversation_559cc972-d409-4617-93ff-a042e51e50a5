import {
  Box,
  Button,
  Icon,
  IconButton,
  List,
  ListItem,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import copy from 'copy-to-clipboard';
import { type FC, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { FiCopy, FiInfo } from 'react-icons/fi';
import { useShowMessage } from 'shared/hooks/alerts';
import { useMerchantDetails } from 'shared/hooks/merchant';

import { GenerateAPIkeyModal } from './GenerateAPIkeyModal';

type SecretValue = 'secret_key' | 'shop_id';

const listData: Array<{
  id: number;
  label: string;
  value: SecretValue;
}> = [
  { id: 1, label: 'shop-id', value: 'shop_id' },
  { id: 2, label: 'secret-api-key', value: 'secret_key' },
];

export const ApiDetails: FC = () => {
  const { t } = useTranslation('dev-tools');
  const { data: merchantData } = useMerchantDetails();
  const showMessage = useShowMessage();

  const onCopyClick = useCallback(
    (value: SecretValue, label: string) => {
      // @ts-expect-error: copy-to-clipboard types are not correct
      copy(merchantData?.merchant?.[value]);
      showMessage(t(`api-details.notifications.${label}-saved`), FiCopy);
    },
    [merchantData?.merchant, showMessage, t],
  );
  const { open, onOpen, onClose } = useDisclosure();

  const onSuccess = useCallback(() => {
    onClose();
    showMessage(t('api-details.modal-success'));
  }, [onClose, showMessage, t]);

  return (
    <Box mb={[12, 16]}>
      <Text mb={[3, 2]} textStyle="h4">
        {t('api-details.title')}
      </Text>
      <Box
        alignItems="center"
        backgroundColor="primary.100"
        borderRadius="4px"
        color="primary.900"
        display="flex"
        mb={3}
        px={3}
        py={2}
      >
        <Icon as={FiInfo} boxSize={5} color="primary.700" mr={2} />
        <Text textStyle="body2">{t('api-details.secret')}</Text>
      </Box>
      <List
        border="1px solid"
        borderColor="neutral.100"
        borderRadius="8px"
        mb={3}
      >
        {listData.map(({ id, label, value }) => (
          <ListItem
            alignItems="center"
            borderBottom={`${id !== listData.length ? '1px' : '0'} solid`}
            borderColor="neutral.100"
            display="flex"
            key={id}
            px={4}
            py={2.5}
          >
            <Box color="neutral.900" mr={3}>
              <Text textStyle="body2">{t(`api-details.${label}`)}</Text>
              <Text
                data-cy={`api-details-${label}`}
                textStyle="body2-highlight"
              >
                {merchantData?.merchant?.[value]}
              </Text>
            </Box>
            {!!merchantData?.merchant?.[value] && (
              <IconButton
                aria-label="copy"
                ml="auto"
                onClick={() => {
                  onCopyClick(value, label);
                }}
                variant="ghost"
              >
                <Icon as={FiCopy} boxSize={6} color="primary.800" />
              </IconButton>
            )}
          </ListItem>
        ))}
      </List>
      <Button
        data-cy="generate-api-key-modal-open"
        onClick={onOpen}
        width="full"
      >
        {t('api-details.generate-keys')}
      </Button>
      <GenerateAPIkeyModal
        isOpen={open}
        onClose={onClose}
        onSuccess={onSuccess}
      />
    </Box>
  );
};
