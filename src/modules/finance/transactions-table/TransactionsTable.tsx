import { useReactiveVar } from '@apollo/client';
import { Box, type TypographyProps, VStack } from '@chakra-ui/react';
import { Table, Tbody, Th, Thead, Tr } from '@chakra-ui/table';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Loader, NoData } from 'shared/components';
import { useGetPages, useSetDefaultTableState } from 'shared/hooks/utils';

import {
  FinanceTableControls,
  TransactionsCollectionByPage,
} from './components';
import { useTransactionsData } from './TransactionsTable.hooks';
import {
  defaultTransactionsTableState,
  transactionsTableState,
} from './TransactionsTable.utils';

const TABLE_COLUMNS: Array<{
  name: string;
  textAlign?: TypographyProps['textAlign'];
}> = [
  {
    name: 'table.order-ref',
  },
  {
    name: 'table.client',
  },
  {
    name: 'table.amount',
    textAlign: 'right',
  },
  {
    name: 'table.method',
  },
  {
    name: 'table.type',
  },
  {
    name: 'table.explanation',
  },
  {
    name: 'table.date',
  },
];

export const TransactionsTable = () => {
  const { t } = useTranslation('finance');
  const tableState = useReactiveVar(transactionsTableState);
  const { pages } = useGetPages(tableState.page);
  const [portalEl, setPortalEl] = useState<HTMLDivElement | null>(null);
  const { loading, data } = useTransactionsData({
    page: 1,
  });

  useSetDefaultTableState(
    transactionsTableState,
    defaultTransactionsTableState,
  );

  return (
    <VStack align="stretch" height="100%" overflow="auto" gap={6}>
      <FinanceTableControls />
      {loading ? (
        <Loader height="65vh" />
      ) : data?.transactions?.data?.length ? (
        <Box mx={-5} overflow="auto">
          <Box minW="100%" px={[5, null, 10]} width="fit-content">
            <Table data-cy="transactions-table" position="relative">
              <Thead
                bg="white"
                borderBottom="1px solid"
                borderColor="neutral.100"
                left={0}
                position="sticky"
                top={0}
              >
                <Tr>
                  {TABLE_COLUMNS.map(({ name, textAlign }) => (
                    <Th
                      borderBottom="1px solid"
                      borderColor="neutral.100"
                      color="neutral.800"
                      key={name}
                      pl={[0, null, 3]}
                      pr={5}
                      py={2}
                      textAlign={textAlign}
                      textStyle="body2-highlight"
                      textTransform="none"
                      whiteSpace="nowrap"
                    >
                      {t(name)}
                    </Th>
                  ))}
                </Tr>
              </Thead>
              <Tbody>
                {pages.map((page) => (
                  <TransactionsCollectionByPage
                    key={page}
                    page={page}
                    portalElement={portalEl}
                  />
                ))}
              </Tbody>
            </Table>
          </Box>
          <Box
            px={[0, null, 5]}
            ref={(ref) => {
              setPortalEl(ref);
            }}
          />
        </Box>
      ) : (
        <NoData height="100%" text={t('no-data')} />
      )}
    </VStack>
  );
};
