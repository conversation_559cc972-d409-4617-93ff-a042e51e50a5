import { IconButton, useDisclosure } from '@chakra-ui/react';
import { Tooltip } from '@chakra-ui/tooltip';
import { useTranslation } from 'react-i18next';
import { TiDeleteOutline } from 'react-icons/ti';
import { MerchantApplicationDetailsDocument } from 'shared/api';
import {
  ApplicationDetailsDisclaimer,
  type ApplicationDetailsDisclaimerProps,
  UndoApplicationRequestDialog,
} from 'shared/components/application';
import {
  LocizeApplicationsKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';
import { useShowError } from 'shared/hooks/alerts';
import { useApplicationId } from 'shared/hooks/application';

import { useDeleteApplicationCancellationRequestMutation } from './api';

type Props = {
  requestId: number;
} & ApplicationDetailsDisclaimerProps;

export const ApplicationCancellationDisclaimer = ({
  date,
  requestId,
  type,
  requestCreatorFullName,
}: Props) => {
  const { t } = useTranslation(LocizeNamespaces.APPLICATIONS);
  const { open, onClose, onOpen } = useDisclosure();
  const applicationId = useApplicationId();

  const showError = useShowError();

  const [deleteRequest, { loading }] =
    useDeleteApplicationCancellationRequestMutation({
      variables: {
        requestId,
      },
      refetchQueries: [
        {
          query: MerchantApplicationDetailsDocument,
          variables: { applicationId },
        },
      ],
      awaitRefetchQueries: true,
    });

  const handleDeleteRequestSubmit = async () => {
    try {
      await deleteRequest();
    } catch (error) {
      showError(
        "Can't cancel application cancellation. Please, try again later.",
      );
      if (error instanceof Error) {
        throw new Error(error?.message);
      }
    }
  };

  return (
    <>
      <ApplicationDetailsDisclaimer
        after={
          <Tooltip label={t(LocizeApplicationsKeys.APPLICATION_UPDATE_UNDO)}>
            <IconButton
              aria-label="delete-modification"
              color="primary.700"
              disabled={loading}
              fontSize="20px"
              onClick={onOpen}
              size="sm"
              variant="ghost"
            >
              <TiDeleteOutline />
            </IconButton>
          </Tooltip>
        }
        date={date}
        requestCreatorFullName={requestCreatorFullName}
        type={type}
      />
      {!!open && (
        <UndoApplicationRequestDialog
          isLoading={loading}
          isOpen
          onClose={onClose}
          onSubmit={handleDeleteRequestSubmit}
        />
      )}
    </>
  );
};
