import { Button, useDisclosure } from '@chakra-ui/react';
import { AmountModificationModal } from 'modules/applications/details/amount-modification-modal';
import { CancellationModal } from 'modules/applications/details/cancellation-modal';
import { useTranslation } from 'react-i18next';
import { FiEdit, FiXCircle } from 'react-icons/fi';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import { LocizeNamespaces } from 'shared/constants/localization-keys';
import type { ApplicationType } from 'shared/types';

type ActionButtonProps = {
  icon: JSX.Element;
  text: string;
  onClick: () => void;
  dataCy: string;
  colorScheme?: ColorSchemes;
};

const ActionButton = ({
  text,
  icon,
  onClick,
  dataCy,
  colorScheme = ColorSchemes.PRIMARY,
}: ActionButtonProps) => {
  return (
    <Button
      colorScheme={colorScheme}
      data-cy={dataCy}
      justifyContent="flex-start"
      onClick={onClick}
      size="sm"
      variant="ghost"
      width="full"
    >
      {icon}
      {text}
    </Button>
  );
};

type Props = Pick<
  ApplicationType,
  'id' | 'latest_modification_request' | 'requested_amount' | 'schedule_type'
> & {
  minMaxModificationAmount: {
    min: number;
    max: number;
  };
};

export const ApplicationModifications = ({
  id,
  latest_modification_request,
  requested_amount,
  schedule_type,
  minMaxModificationAmount,
}: Props) => {
  const { t } = useTranslation(LocizeNamespaces.APPLICATIONS);

  const amountModificationDisclosure = useDisclosure();
  const cancellationDisclosure = useDisclosure();

  return (
    <div>
      {!latest_modification_request && (
        <ActionButton
          dataCy="application-amount-modification-toggle"
          icon={<FiEdit />}
          onClick={amountModificationDisclosure.onOpen}
          text={t('details.amount-modification')}
        />
      )}
      <ActionButton
        colorScheme={ColorSchemes.RED}
        dataCy="application-cancellation-toggle"
        icon={<FiXCircle />}
        onClick={cancellationDisclosure.onOpen}
        text={t('details.cancellation')}
      />
      {!!amountModificationDisclosure.isOpen && (
        <AmountModificationModal
          applicationId={id}
          initialAmount={requested_amount}
          isOpen
          onClose={amountModificationDisclosure.onClose}
          scheduleType={schedule_type}
          minMaxModificationAmount={minMaxModificationAmount}
        />
      )}
      {!!cancellationDisclosure.isOpen && (
        <CancellationModal
          applicationId={id}
          isOpen
          onClose={cancellationDisclosure.onClose}
        />
      )}
    </div>
  );
};
