import {
  Box,
  Button,
  Icon,
  IconButton,
  Text,
  useBreakpointValue,
} from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { FiArrowLeft } from 'react-icons/fi';
import { Link } from 'react-router-dom';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import { ApplicationListRoute } from 'shared/constants/routes';

export const DesktopHeader = () => {
  const { t } = useTranslation('applications');
  const shouldWrapNav = useBreakpointValue({ base: true, lg: false });

  return (
    <Box
      alignItems="center"
      display="flex"
      h={12}
      mb={3}
      mt={shouldWrapNav ? 5 : 10}
      mx={shouldWrapNav ? 5 : 10}
      position="relative"
    >
      {!shouldWrapNav && (
        <Button
          as={Link}
          colorScheme={ColorSchemes.PRIMARY}
          left={0}
          position="absolute"
          to={ApplicationListRoute.format()}
          top={0}
          variant="ghost"
        >
          <FiArrowLeft />
          {t('details.back')}
        </Button>
      )}
      <Box mx="auto" position="relative" w="400px">
        {!!shouldWrapNav && (
          <IconButton
            aria-label="back"
            as={Link}
            left={-2}
            position="absolute"
            to={ApplicationListRoute.format()}
            top="50%"
            transform="translate(-100%, -50%)"
            variant="ghost"
          >
            <Icon as={FiArrowLeft} boxSize={6} color="primary.800" />
          </IconButton>
        )}
        <Text textStyle="h3">{t('details.title')}</Text>
      </Box>
    </Box>
  );
};
