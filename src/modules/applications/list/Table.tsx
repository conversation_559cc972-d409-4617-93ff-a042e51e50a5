import { useReactiveVar } from '@apollo/client';
import { Box, type BoxProps, Button } from '@chakra-ui/react';
import { Table, Tbody, Td, Th, Thead, Tr } from '@chakra-ui/table';
import { memo, useContext, useState } from 'react';
import { createPortal } from 'react-dom';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  MerchantApplicationsOrderBy,
  type MerchantApplicationsQuery,
  useMerchantApplicationsQuery,
} from 'shared/api';
import { Loader, NoData } from 'shared/components';
import { ApplicationDetailsRoute } from 'shared/constants/routes';
import { GlobalStateContext } from 'shared/hooks/state';
import { useCurrentUser } from 'shared/hooks/user';
import { useDebouncedValue, useGetPages } from 'shared/hooks/utils';

import {
  ApplicationClientName,
  applicationsTableState,
  getStatusUpdatedDate,
  getStatusWithBadge,
  PaymentMethodBadge,
  StatusBadge,
} from '../shared';

const PER_PAGE = 50;

const useApplicationsPageData = (page: number) => {
  const { merchantId } = useContext(GlobalStateContext);
  const statuses = useReactiveVar(applicationsTableState.statuses);
  const isIssuedByMe = useReactiveVar(applicationsTableState.issuedByMe);
  const search = useReactiveVar(applicationsTableState.search);
  const paymentMethods = useReactiveVar(applicationsTableState.paymentMethods);

  const { user } = useCurrentUser();

  return useMerchantApplicationsQuery({
    skip: !merchantId,
    variables: {
      merchantId: merchantId ?? -1,
      page,
      limit: PER_PAGE,
      orderBy: MerchantApplicationsOrderBy.CREATED_AT,
      statuses: useDebouncedValue(statuses, 500),
      createdByUserId: isIssuedByMe ? user?.id : undefined,
      merchantRefOrUserName: search,
      scheduleTypes: useDebouncedValue(paymentMethods, 500),
    },
  });
};

const borderStyles: BoxProps = {
  borderBottom: '1px solid',
  borderColor: 'neutral.100',

  sx: {
    'tbody tr:last-of-type > &': {
      borderBottom: 'none',
    },
  },
};

const spacing: BoxProps = {
  pl: [0, null, 3],
  pr: 5,
  py: 2,
};

const headerCellStyles: BoxProps = {
  ...borderStyles,
  ...spacing,
  whiteSpace: 'nowrap',
  textTransform: 'none',
  textStyle: 'body2-highlight',
  color: 'neutral.800',
};

const cellStyles: BoxProps = {
  ...borderStyles,
  ...spacing,
  py: [3, 2],
  textStyle: 'body2',
  color: 'neutral.must',
  whiteSpace: 'nowrap',
};

export const ApplicationsTable = () => {
  const { t } = useTranslation('applications');
  const page = useReactiveVar(applicationsTableState.page);
  const { pages } = useGetPages(page);
  const [portalEl, setPortalEl] = useState<HTMLDivElement | null>(null);

  const { loading, data } = useApplicationsPageData(1);

  return (
    <>
      {loading ? (
        <Loader height="70vh" />
      ) : data?.applications?.data?.length ? (
        <Box mx={-5} overflow="auto">
          <Box minW="100%" px={[5, null, 10]} width="fit-content">
            <Table data-cy="applications-table">
              <Thead>
                <Tr>
                  <Th {...headerCellStyles}>{t('table.order-ref')}</Th>
                  <Th {...headerCellStyles}>{t('table.status')}</Th>
                  <Th {...headerCellStyles}>{t('table.client')}</Th>
                  <Th {...headerCellStyles} textAlign="right">
                    {t('table.amount')}
                  </Th>
                  <Th {...headerCellStyles}>{t('table.method')}</Th>
                  <Th {...headerCellStyles}>{t('table.status-updated')}</Th>
                  <Th {...headerCellStyles}>{t('table.cashier')}</Th>
                  <Th {...headerCellStyles}>{t('table.store')}</Th>
                </Tr>
              </Thead>
              <Tbody>
                {pages.map((p) => (
                  <ApplicationTablePage
                    isLastRenderedPage={page === p}
                    key={p}
                    page={p}
                    portalElement={portalEl}
                  />
                ))}
              </Tbody>
            </Table>
          </Box>
        </Box>
      ) : (
        <NoData text={t('no-data')} />
      )}

      <Box
        pt={5}
        px={[0, null, 5]}
        ref={(ref) => {
          setPortalEl(ref);
        }}
      />
    </>
  );
};

type ApplicationTablePageProps = {
  page: number;
  portalElement: HTMLDivElement | null;
  isLastRenderedPage: boolean;
};

const ApplicationTablePage = memo(
  ({ page, portalElement, isLastRenderedPage }: ApplicationTablePageProps) => {
    const { t } = useTranslation('applications');
    const { loading, data } = useApplicationsPageData(page);

    if (loading) {
      return (
        portalElement && createPortal(<Loader height="5rem" />, portalElement)
      );
    }

    if (!data?.applications?.data?.length) {
      return null;
    }

    const moreApplicationsToLoad =
      data.applications.total -
      (page - 1) * PER_PAGE -
      data.applications.data.length;

    return (
      <>
        {data.applications.data.map((app) => {
          if (!app) {
            return null;
          }

          return <TableRow key={app.id} {...app} />;
        })}

        {!!isLastRenderedPage &&
          !!data?.applications?.has_more_pages &&
          !!portalElement &&
          createPortal(
            <Button
              data-cy="applications-load-more"
              onClick={() => applicationsTableState.page(page + 1)}
              width="full"
            >
              {t('load-more', {
                value: Math.min(moreApplicationsToLoad, PER_PAGE),
              })}
            </Button>,
            portalElement,
          )}
      </>
    );
  },
);

const TableRow = memo(
  (
    app: NonNullable<
      NonNullable<MerchantApplicationsQuery['applications']>['data']
    >[number],
  ) => {
    const navigate = useNavigate();
    const { t, i18n } = useTranslation('applications');

    if (!app) return null;

    const {
      id,
      merchant_data,
      invoice_reference_nr,
      for_private_person,
      legal_person_info,
      user_info,
      requested_amount,
      schedule_type,
      application_reference,
      from_retail,
      status,
      user_id,
      signed_at,
      rejected_at,
      processed_at,
      created_at,
    } = app;

    return (
      <Tr
        cursor="pointer"
        onClick={() => {
          navigate(ApplicationDetailsRoute.format({ applicationId: id }));
        }}
      >
        <Td {...cellStyles}>
          {merchant_data?.reference ?? invoice_reference_nr}
        </Td>
        <Td {...cellStyles}>
          <StatusBadge
            status={getStatusWithBadge({
              status,
              user_id,
              schedule_type,
            })}
            withTooltip
          />
        </Td>
        <Td
          {...cellStyles}
          maxWidth="200px"
          overflow="hidden"
          textOverflow="ellipsis"
          whiteSpace="nowrap"
        >
          <ApplicationClientName
            for_private_person={for_private_person}
            legal_person_info={legal_person_info}
            user_info={user_info}
          />
        </Td>
        <Td {...cellStyles} textAlign="right">
          {requested_amount.toLocaleString(i18n.language, {
            maximumFractionDigits: 2,
            minimumFractionDigits: 2,
          })}
          €
        </Td>
        <Td {...cellStyles}>
          <PaymentMethodBadge paymentMethod={schedule_type} />
        </Td>
        <Td {...cellStyles}>
          {getStatusUpdatedDate({
            signed_at,
            rejected_at,
            processed_at,
            created_at,
          })}
        </Td>
        <Td {...cellStyles}>
          {application_reference?.creator?.profile
            ? [
                application_reference.creator.profile.first_name,
                application_reference.creator.profile.last_name,
              ].join(' ')
            : '-'}
        </Td>
        <Td {...cellStyles}>
          {from_retail
            ? (merchant_data?.store?.name ?? t('table.retail'))
            : t('table.online')}
        </Td>
      </Tr>
    );
  },
);
