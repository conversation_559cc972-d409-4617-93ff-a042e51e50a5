import { Box, Icon, IconButton, Text } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { FiEdit, FiTrash2 } from 'react-icons/fi';
import { Confirmation } from 'shared/components';

type StoreListItemProps = {
  name: string;
  isDisabled?: boolean;
  onEdit: () => void;
  onDelete: () => void | Promise<void>;
  dataCy?: string;
};
export const StoreListItem = ({
  name,
  isDisabled,
  dataCy,
  onEdit,
  onDelete,
}: StoreListItemProps) => {
  const { t } = useTranslation('settings');

  return (
    <Box
      alignItems="center"
      borderBottom="1px solid"
      borderColor="neutral.100"
      borderLeft="1px solid"
      borderRight="1px solid"
      borderTopWidth={0}
      css={{
        '&:first-of-type': {
          borderTopWidth: '1px',
          borderTopLeftRadius: '4px',
          borderTopRightRadius: '4px',
        },
        '&:last-of-type': {
          borderBottomLeftRadius: '4px',
          borderBottomRightRadius: '4px',
        },
      }}
      data-cy={dataCy}
      display="flex"
      h={14}
      p={1}
      w="100%"
    >
      <Text data-cy="store-list-item-name" ml={3} textStyle="body1-highlight">
        {name}
      </Text>
      <Box ml="auto">
        <IconButton
          aria-label="edit"
          data-cy="store-list-item-edit-btn"
          icon={<Icon as={FiEdit} boxSize={6} color="primary.800" />}
          disabled={isDisabled}
          onClick={onEdit}
          variant="ghost"
        />
        <Confirmation
          actionText={t('stores.delete-confirmation.submit')}
          cyPrefix="delete-store"
          onAction={onDelete}
          popoverPlacement="bottom-end"
          title={t('stores.delete-confirmation.title')}
          trigger={
            <IconButton
              aria-label="delete"
              data-cy="store-list-item-delete-btn"
              icon={<Icon as={FiTrash2} boxSize={6} color="primary.800" />}
              disabled={isDisabled}
              variant="ghost"
            />
          }
        />
      </Box>
    </Box>
  );
};
