import { Box, Icon, IconButton, SimpleGrid } from '@chakra-ui/react';
import { Tab, TabList, Tabs } from '@chakra-ui/tabs';
import { useTranslation } from 'react-i18next';
import { FiArrowLeft } from 'react-icons/fi';
import { Link, Outlet, useNavigate } from 'react-router-dom';
import { Header } from 'shared/components';
import { DevToolsTabRoute, ForDeveloperRoute } from 'shared/constants/routes';
import { useIsMobileLayout } from 'shared/hooks/layout';
import { DevToolsRouteTabs } from 'shared/types';

import { useDevToolsTabByPathname } from './DevToolsLayout.hooks';

const TABS = [
  DevToolsRouteTabs.Setup,
  DevToolsRouteTabs.Urls,
  DevToolsRouteTabs.Calculator,
];

export const DevToolsLayout = () => {
  const { t } = useTranslation('dev-tools');
  const tab = useDevToolsTabByPathname();
  const navigate = useNavigate();
  const isMobileLayout = useIsMobileLayout();

  const renderHeader = () => {
    if (isMobileLayout) {
      return (
        <Header
          before={
            <Link to={ForDeveloperRoute.format()}>
              <IconButton aria-label="back" mx={2} my={1} variant="ghost">
                <Icon as={FiArrowLeft} boxSize={6} color="primary.800" />
              </IconButton>
            </Link>
          }
          isSidebarButtonEnabled={false}
          title={t(`tabs.${tab}`)}
        />
      );
    }

    return (
      <Tabs
        align="center"
        bg="white"
        index={TABS.indexOf(tab)}
        isManual
        left={0}
        onChange={(index) => {
          navigate(DevToolsTabRoute.format({ tab: TABS[index] }));
        }}
        position="sticky"
        top={0}
        width="100%"
        zIndex={2}
      >
        <TabList>
          {TABS.map((tab) => (
            <Tab key={tab} value={DevToolsRouteTabs.Setup}>
              {t(`tabs.${tab}`)}
            </Tab>
          ))}
        </TabList>
      </Tabs>
    );
  };

  return (
    <SimpleGrid gridTemplateRows="auto 1fr">
      {renderHeader()}
      <Box px="20px">
        <Outlet />
      </Box>
    </SimpleGrid>
  );
};
