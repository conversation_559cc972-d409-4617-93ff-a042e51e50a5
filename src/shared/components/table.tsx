import { type BoxProps } from '@chakra-ui/react';
import { Td as ChakraTd, Th as ChakraTh } from '@chakra-ui/table';
import type { PropsWithChildren } from 'react';

export const Td = ({ children, ...props }: PropsWithChildren<BoxProps>) => (
  <ChakraTd
    borderBottom="1px solid"
    borderColor="neutral.100"
    color="neutral.must"
    pl={[0, null, 3]}
    pr={5}
    py={[3, 2]}
    textStyle="body2"
    whiteSpace="nowrap"
    {...props}
  >
    {children}
  </ChakraTd>
);

export const Th = ({ children, ...props }: PropsWithChildren<BoxProps>) => (
  <ChakraTh
    borderBottom="1px solid"
    borderColor="neutral.100"
    color="neutral.800"
    pl={[0, null, 3]}
    pr={5}
    py={2}
    textStyle="body2-highlight"
    textTransform="none"
    whiteSpace="nowrap"
    {...props}
  >
    {children}
  </ChakraTh>
);
