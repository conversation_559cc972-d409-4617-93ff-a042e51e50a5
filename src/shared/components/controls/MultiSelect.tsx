import {
  FormControl,
  FormHelperText,
  FormLabel,
} from '@chakra-ui/form-control';
import { Input, InputGroup, InputRightElement } from '@chakra-ui/input';
import { usePopper } from '@chakra-ui/popper';
import {
  Box,
  Button,
  chakra,
  HStack,
  Icon,
  type Placement,
  Portal,
  Switch,
  Text,
} from '@chakra-ui/react';
import { useSelect } from 'downshift';
import { motion, type Variants } from 'framer-motion';
import type React from 'react';
import { useCallback, useMemo } from 'react';

// Simple ref merger for Chakra UI v3 compatibility
const mergeRefs = <T,>(refs: Array<React.Ref<T> | undefined>) => {
  return (node: T) => {
    refs.forEach((ref) => {
      if (typeof ref === 'function') {
        ref(node);
      } else if (ref != null) {
        (ref as React.MutableRefObject<T | null>).current = node;
      }
    });
  };
};
import { useTranslation } from 'react-i18next';
import { FiChevronDown } from 'react-icons/fi';

import { ColorSchemes } from '../../chakra-theme/foundations/colors';
import type { BaseInputProps, SelectItemType } from './common';

export type MultiSelectProps<VType, ItemType extends SelectItemType<VType>> = {
  items: Array<ItemType>;
  defaultValue?: Array<ItemType> | null;
  value?: Array<ItemType> | null;
  withResetBtn?: boolean;
  placement?: Placement;
  allSelectedText?: string;
  maxSelectedShown?: number;
  renderItem?: (item: ItemType, isSelected?: boolean) => React.ReactNode;
  valueComparator?: (a: ItemType, b: ItemType) => boolean;
  onChange?: (ItemType: Array<ItemType>) => void;
} & BaseInputProps;

const defaultRenderItem = <ItemType extends SelectItemType>(
  item: ItemType,
  isSelected: boolean,
): React.ReactNode => (
  <Text textStyle={isSelected ? 'body1-highlight' : 'body1'}>{item.label}</Text>
);

const motionVariants: Variants = {
  enter: {
    visibility: 'visible',
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.2,
      ease: [0.4, 0, 0.2, 1],
    },
  },
  exit: {
    transitionEnd: {
      visibility: 'hidden',
    },
    opacity: 0,
    scale: 0.8,
    transition: {
      duration: 0.1,
      easings: 'easeOut',
    },
  },
};

const Motion = chakra(motion.div);

function isItemSelected<VType, ItemType extends SelectItemType<VType>>(
  selectedItems: Array<ItemType>,
  item: ItemType,
  comparator: (a: ItemType, b: ItemType) => boolean,
): boolean {
  return !!selectedItems.find((selected) => comparator(selected, item));
}

export const MultiSelect = <VType, ItemType extends SelectItemType<VType>>({
  label,
  name,
  items,
  hint,
  isDisabled,
  defaultValue,
  value,
  placement = 'bottom',
  allSelectedText,
  maxSelectedShown = 1,
  withResetBtn,
  // @ts-expect-error: type error
  renderItem = defaultRenderItem,
  valueComparator = (a: ItemType, b: ItemType) => a === b,
  onChange,
  ...chackraProps
}: MultiSelectProps<VType, ItemType>) => {
  const { t } = useTranslation('common');
  const selectedItems = useMemo(() => value || [], [value]);
  const isAllSelected = selectedItems.length === items.length;
  const shouldHideSelected = selectedItems.length > maxSelectedShown;
  const isDirty = useMemo(
    () =>
      !!(
        defaultValue &&
        !(
          defaultValue.length === selectedItems.length &&
          defaultValue.every(
            (defaultItem) =>
              !!selectedItems.find((item) =>
                valueComparator(item, defaultItem),
              ),
          )
        )
      ),
    [defaultValue, selectedItems, valueComparator],
  );

  const {
    open,
    getToggleButtonProps,
    getLabelProps,
    getMenuProps,
    highlightedIndex,
    openMenu,
    closeMenu,
    getItemProps,
    setHighlightedIndex,
  } = useSelect({
    items,
    selectedItem: null,
    itemToString: (item) => item?.label ?? '',
    stateReducer: (state, actionAndChanges) => {
      const { changes, type } = actionAndChanges;
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      changes.highlightedIndex;
      switch (type) {
        case useSelect.stateChangeTypes.ToggleButtonKeyDownEnter:
        case useSelect.stateChangeTypes.ToggleButtonKeyDownSpaceButton:
        case useSelect.stateChangeTypes.ItemClick:
          return {
            ...changes,
            highlightedIndex: state.highlightedIndex,
            isOpen: true, // keep the menu open after selection.
          };
      }
      return changes;
    },
    onStateChange: ({ type, selectedItem }) => {
      switch (type) {
        case useSelect.stateChangeTypes.ToggleButtonKeyDownEnter:
        case useSelect.stateChangeTypes.ToggleButtonKeyDownSpaceButton:
        case useSelect.stateChangeTypes.ItemClick:
          if (selectedItem) {
            onChange?.(
              isItemSelected(selectedItems, selectedItem, valueComparator)
                ? selectedItems?.filter(
                    (selected) => !valueComparator(selected, selectedItem),
                  )
                : [...selectedItems, selectedItem],
            );
          }
          break;
        default:
          break;
      }
    },
  });

  // popper hook used to position menu element relative to trigger
  const { popperRef, referenceRef } = usePopper({
    placement,
    matchWidth: true,
    offset: [0, 0],
  });

  // styles applied to input when menu is opened
  const inputStyles = useMemo(
    () =>
      open
        ? {
            px: 'calc(1rem - 1px)',
            border: '2px solid',
            borderColor: 'neutral.900',
          }
        : {},
    [open],
  );

  const openOnEnterPressed = useCallback(
    (e: React.KeyboardEvent<HTMLElement>) => {
      if (e.key === 'Enter') {
        openMenu();
        e.preventDefault();
        e.stopPropagation();
      }
    },
    [openMenu],
  );

  const toggleProps = getToggleButtonProps({
    disabled: isDisabled,
    onKeyDown: openOnEnterPressed,
  });
  const menuProps = getMenuProps({}, { suppressRefError: true });

  return (
    <Box w="fit-content" {...chackraProps}>
      <FormControl isDisabled={isDisabled}>
        {!!label && <FormLabel {...getLabelProps()}>{label}</FormLabel>}
        <InputGroup>
          <Input
            {...toggleProps}
            cursor="pointer"
            isReadOnly
            name={name}
            ref={mergeRefs([referenceRef, toggleProps.ref])}
            type="text"
            {...inputStyles}
            h="100%"
            position="absolute"
            w="100%"
          />

          <HStack
            alignItems="center"
            data-cy="multi-select-value"
            display="flex"
            h={10}
            pl={4}
            pointerEvents="none"
            pr={10}
            gap={1}
            w="auto"
            zIndex="2"
          >
            {isAllSelected && allSelectedText ? (
              <Text textStyle="body1">{allSelectedText}</Text>
            ) : (
              selectedItems
                .slice(0, shouldHideSelected ? 1 : maxSelectedShown)
                .map((item) => <Box key={item.label}>{renderItem(item)}</Box>)
            )}
            {!!shouldHideSelected && !isAllSelected && (
              <Text pl={1} textStyle="caption-highlight">
                {t('select.more-items', { value: selectedItems.length - 1 })}
              </Text>
            )}
          </HStack>

          <InputRightElement pointerEvents="none">
            <Icon
              as={FiChevronDown}
              boxSize="1.5rem"
              color="neutral.900"
              transform={open ? 'rotate(180deg)' : 'rotate(0)'}
            />
          </InputRightElement>
        </InputGroup>
        {!!hint && <FormHelperText>{hint}</FormHelperText>}
      </FormControl>
      <Portal>
        <Box
          data-cy="multiselect-menu"
          {...menuProps}
          outline="none"
          ref={mergeRefs([popperRef, menuProps.ref])}
          zIndex={open ? 'dropdown' : -1}
        >
          <Motion
            animate={open ? 'enter' : 'exit'}
            bg="white"
            borderRadius="4px"
            boxShadow="0px 1px 8px rgba(0, 0, 0, 0.13), 0px 8px 16px rgba(0, 0, 0, 0.07);"
            initial={false}
            maxHeight="20rem"
            overflow="auto"
            py={2}
            variants={motionVariants}
          >
            {!!open &&
              items.map((item, index) => {
                const isSelected = isItemSelected(
                  selectedItems,
                  item,
                  valueComparator,
                );
                return (
                  <Box
                    alignItems="center"
                    bg={
                      highlightedIndex === index ? 'neutral.50' : 'transparent'
                    }
                    cursor="pointer"
                    data-cy="multiselect-menu-item"
                    display="flex"
                    justifyContent="space-between"
                    key={item.label}
                    px={4}
                    py={2}
                    {...getItemProps({ item, index })}
                  >
                    {renderItem(item, isSelected)}

                    <Switch.Root
                      colorPalette={ColorSchemes.PRIMARY}
                      data-cy="multiselect-menu-item-switch"
                      checked={isSelected}
                      onClick={(e) => {
                        e.preventDefault();
                      }}
                      pointerEvents="none"
                    >
                      <Switch.HiddenInput />
                      <Switch.Control>
                        <Switch.Thumb />
                      </Switch.Control>
                    </Switch.Root>
                  </Box>
                );
              })}
            {!!defaultValue && !!isDirty && !!withResetBtn && (
              <Button
                colorScheme={ColorSchemes.PRIMARY}
                data-cy="multiselect-reset"
                mt={2}
                onClick={() => {
                  onChange?.(defaultValue);
                  closeMenu();
                }}
                onMouseEnter={() => {
                  setHighlightedIndex(-1);
                }}
                variant="ghost"
                width="full"
              >
                {t('select.reset')}
              </Button>
            )}
          </Motion>
        </Box>
      </Portal>
    </Box>
  );
};
