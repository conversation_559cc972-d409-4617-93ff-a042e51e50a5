import {
  AlertDialog,
  AlertDialogCloseButton,
  AlertDialogContent,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogOverlay,
} from '@chakra-ui/modal';
import { Button } from '@chakra-ui/react';
import { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import {
  LocizeApplicationsKeys,
  LocizeCommonKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';

import { ButtonWithLoader } from '../ButtonWithLoader';

type Props = {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: () => void;
  isLoading: boolean;
};

export const UndoApplicationRequestDialog = ({
  open,
  onClose,
  isLoading,
  onSubmit,
}: Props) => {
  const { t } = useTranslation(LocizeNamespaces.APPLICATIONS);
  const { t: tc } = useTranslation(LocizeNamespaces.COMMON);

  const cancelRef = useRef<HTMLButtonElement>(null);

  return (
    <AlertDialog
      isCentered
      isOpen={open}
      leastDestructiveRef={cancelRef}
      motionPreset="slideInBottom"
      onClose={onClose}
    >
      <AlertDialogOverlay />
      <AlertDialogContent width="400px">
        <AlertDialogHeader>
          {t(LocizeApplicationsKeys.UNDO_REQUEST_MODAL_TITLE)}
        </AlertDialogHeader>
        <AlertDialogCloseButton />
        <AlertDialogFooter>
          <Button
            colorScheme={ColorSchemes.SECONDARY}
            disabled={isLoading}
            onClick={onClose}
            ref={cancelRef}
          >
            {tc(LocizeCommonKeys.FORMS_CANCEL)}
          </Button>
          <ButtonWithLoader loading={isLoading} ml={3} onClick={onSubmit}>
            {tc(LocizeCommonKeys.FORMS_SUBMIT)}
          </ButtonWithLoader>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
